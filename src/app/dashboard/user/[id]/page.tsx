"use client"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  ChevronDown,
  Reply,
  Star,
  User as UserIcon
} from "lucide-react"
import { useState } from "react"
import { EditReviewDialog } from "./_components/edit-review-dialog"
import { EditUserDialog } from "./_components/edit-user-dialog"
import { type Profile } from "./_components/profile-card"
import { ProfilesSection } from "./_components/profiles-section"
import { RepliesSection } from "./_components/replies-section"
import { type Reply as ReplyType } from "./_components/reply-card"
import { type Review } from "./_components/review-card"
import { ReviewsSection } from "./_components/reviews-section"
import { UserCard, type User } from "./_components/user-card"

// Mock data for reviews
const userReviews: Review[] = [
  {
    id: 1,
    businessName: "The Coffee House",
    rating: 5,
    comment:
      "Amazing coffee and great atmosphere! The baristas are very friendly and knowledgeable. I especially loved their signature latte.",
    date: "2 days ago",
    likes: 12,
    replies: 3,
  },
  {
    id: 2,
    businessName: "Mario's Italian Restaurant",
    rating: 4,
    comment: "Great pasta and excellent service. The tiramisu was absolutely delicious. Will definitely come back!",
    date: "1 week ago",
    likes: 8,
    replies: 1,
  },
  {
    id: 3,
    businessName: "Tech Store Plus",
    rating: 3,
    comment: "Good selection of products but customer service could be improved. Prices are reasonable though.",
    date: "2 weeks ago",
    likes: 5,
    replies: 0,
  },
  {
    id: 4,
    businessName: "Fitness Center Pro",
    rating: 5,
    comment: "Excellent facilities and very clean. The staff is helpful and the equipment is top-notch.",
    date: "3 weeks ago",
    likes: 15,
    replies: 2,
  },
  {
    id: 5,
    businessName: "Book Haven",
    rating: 4,
    comment: "Great selection of books and cozy reading areas. The staff recommendations are always spot on.",
    date: "1 month ago",
    likes: 9,
    replies: 1,
  },
]

export default function ProfilePage() {
  // View selection state
  const [selectedView, setSelectedView] = useState<'profiles' | 'reviews' | 'replies'>('profiles')

  // Dialog state
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isEditReviewDialogOpen, setIsEditReviewDialogOpen] = useState(false)
  const [selectedReview, setSelectedReview] = useState<Review | null>(null)
  const [reviews, setReviews] = useState<Review[]>(userReviews)

  // This would typically come from your authentication system or API
  const [user, setUser] = useState<User>({
    name: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "+****************",
    avatarUrl: "/placeholder.svg?height=128&width=128",
    isVerified: true,
    role: "Administrator",
    initials: "SJ",
    joinDate: "January 2023",
  })

  // Mock data for user profiles
  const userProfiles: Profile[] = [
    {
      id: 1,
      name: "Sarah's Coffee Shop",
      type: "Business Owner",
      description: "Local coffee shop serving artisanal coffee and pastries",
      avatar: "/placeholder.svg?height=64&width=64",
      isActive: true,
      createdDate: "March 2023",
      reviewsCount: 45,
      avgRating: 4.8,
      category: "Food & Beverage",
    },
    {
      id: 2,
      name: "Tech Reviewer Sarah",
      type: "Content Creator",
      description: "Technology product reviews and tutorials",
      avatar: "/placeholder.svg?height=64&width=64",
      isActive: true,
      createdDate: "January 2023",
      reviewsCount: 128,
      avgRating: 4.6,
      category: "Technology",
    },
    {
      id: 3,
      name: "Fitness Coach Sarah",
      type: "Service Provider",
      description: "Personal fitness training and wellness coaching",
      avatar: "/placeholder.svg?height=64&width=64",
      isActive: false,
      createdDate: "June 2023",
      reviewsCount: 23,
      avgRating: 4.9,
      category: "Health & Fitness",
    },
    {
      id: 4,
      name: "Sarah Johnson - Customer",
      type: "Personal",
      description: "Personal profile for reviews and recommendations",
      avatar: "/placeholder.svg?height=64&width=64",
      isActive: true,
      createdDate: "January 2023",
      reviewsCount: 67,
      avgRating: null,
      category: "General",
    },
  ]



  // Mock data for review replies
  const userReplies: ReplyType[] = [
    {
      id: 1,
      originalReview: "Great service at Downtown Cafe!",
      originalReviewer: "John D.",
      businessName: "Downtown Cafe",
      reply: "Thank you for the kind words! We're glad you enjoyed your experience. Hope to see you again soon!",
      date: "3 days ago",
      likes: 6,
    },
    {
      id: 2,
      originalReview: "The food was okay but service was slow...",
      originalReviewer: "Mike R.",
      businessName: "Burger Palace",
      reply:
        "We apologize for the slow service. We've been working on improving our wait times. Please give us another chance!",
      date: "1 week ago",
      likes: 4,
    },
    {
      id: 3,
      originalReview: "Love the new menu items!",
      originalReviewer: "Lisa K.",
      businessName: "Pasta Corner",
      reply: "Thank you! We're always working to bring fresh and exciting dishes to our customers.",
      date: "2 weeks ago",
      likes: 8,
    },
  ]



  // Handler for profile view action
  const handleProfileView = (profile: Profile) => {
    console.log('Viewing profile:', profile)
    // Add your profile view logic here
  }

  // Handler for review edit action
  const handleReviewEdit = (review: Review) => {
    setSelectedReview(review)
    setIsEditReviewDialogOpen(true)
  }

  // Handler for saving review changes
  const handleSaveReview = (updatedReview: Review) => {
    setReviews(prevReviews =>
      prevReviews.map(review =>
        review.id === updatedReview.id ? updatedReview : review
      )
    )
    console.log('Review updated:', updatedReview)
    // Here you would typically make an API call to save the review
  }

  // Handler for reply edit action
  const handleReplyEdit = (reply: ReplyType) => {
    console.log('Editing reply:', reply)
    // Add your reply edit logic here
  }

  // Handler for edit profile action
  const handleEditProfile = () => {
    setIsEditDialogOpen(true)
  }

  // Handler for saving user changes
  const handleSaveUser = (updatedUser: User) => {
    setUser(updatedUser)
    console.log('User updated:', updatedUser)
    // Here you would typically make an API call to save the user
  }

  // Helper function to get view details
  const getViewDetails = (view: 'profiles' | 'reviews' | 'replies') => {
    switch (view) {
      case 'profiles':
        return {
          icon: <UserIcon className="h-4 w-4" />,
          label: `Profiles (${userProfiles.length})`,
          description: 'Manage your different profiles across the platform'
        }
      case 'reviews':
        return {
          icon: <Star className="h-4 w-4" />,
          label: `Reviews made by user (${userReviews.length})`,
          description: 'Reviews you have written'
        }
      case 'replies':
        return {
          icon: <Reply className="h-4 w-4" />,
          label: `Replies (${userReplies.length})`,
          description: 'Your replies to reviews'
        }
    }
  }

  return (
    <div className="min-h-screen bg-muted/30">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 p-6 h-full">
        {/* Profile Sidebar */}
        <div className="lg:col-span-1">
          <UserCard user={user} onEditProfile={handleEditProfile} />
        </div>

        {/* Main Content Area */}
        <div className="lg:col-span-3">
          <Card className="h-full">
            <CardHeader>
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-semibold">Activity Dashboard</h3>
                <div className="flex gap-2">
                  <Badge variant="secondary" className="gap-1">
                    <UserIcon className="h-3 w-3" />
                    {userProfiles.length} Profiles
                  </Badge>
                  <Badge variant="secondary" className="gap-1">
                    <Star className="h-3 w-3" />
                    {userReviews.length} Reviews
                  </Badge>
                  <Badge variant="secondary" className="gap-1">
                    <Reply className="h-3 w-3" />
                    {userReplies.length} Replies
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="h-full">
              <div className="w-full h-full flex flex-col">
                {/* Dropdown for view selection */}
                <div className="mb-6">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full justify-between">
                        <div className="flex items-center gap-2">
                          {getViewDetails(selectedView).icon}
                          {getViewDetails(selectedView).label}
                        </div>
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-full">
                      <DropdownMenuItem
                        onClick={() => setSelectedView('profiles')}
                        className="flex items-center gap-2"
                      >
                        <UserIcon className="h-4 w-4" />
                        Profiles ({userProfiles.length})
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setSelectedView('reviews')}
                        className="flex items-center gap-2"
                      >
                        <Star className="h-4 w-4" />
                        Reviews made by user ({userReviews.length})
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setSelectedView('replies')}
                        className="flex items-center gap-2"
                      >
                        <Reply className="h-4 w-4" />
                        Replies ({userReplies.length})
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Conditional content rendering */}
                <div className="flex-1 h-full">
                  {selectedView === 'profiles' && (
                    <ProfilesSection
                      profiles={userProfiles}
                      onProfileView={handleProfileView}
                    />
                  )}

                  {selectedView === 'reviews' && (
                    <ReviewsSection
                      reviews={reviews}
                      onReviewEdit={handleReviewEdit}
                    />
                  )}

                  {selectedView === 'replies' && (
                    <RepliesSection
                      replies={userReplies}
                      onReplyEdit={handleReplyEdit}
                    />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit User Dialog */}
      <EditUserDialog
        user={user}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSave={handleSaveUser}
      />

      {/* Edit Review Dialog */}
      {selectedReview && (
        <EditReviewDialog
          review={selectedReview}
          open={isEditReviewDialogOpen}
          onOpenChange={setIsEditReviewDialogOpen}
          onSave={handleSaveReview}
        />
      )}
    </div>
  )
}
